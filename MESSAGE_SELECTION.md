# Message Selection Workflow

## Overview
The extension now parses your API response and allows users to select individual messages from the generated options.

## Your API Response Format
```json
{
  "messages": {
    "message1": "Hi Vipin, I admire your dedication to .NET development...",
    "message2": "<PERSON> <PERSON><PERSON><PERSON>, your post about seeking a .NET full stack developer...",
    "message3": "Hi Vipin, I noticed you're open to new opportunities...",
    "message4": "Hi again Vipin, just reaching out to see if you'd...",
    "message5": "Hey Vipin, circling back as a reminder about my recent..."
  },
  "profileName": "Vipin Kothiyal",
  "profileUrl": "https://www.linkedin.com/in/vipin-kothiyal-31871234",
  "profile": {...}
}
```

## New User Experience

### Step 4: Message Generation & Selection

1. **Select Profiles** → User chooses profiles to generate messages for
2. **Click "Generate Messages"** → API called for each selected profile
3. **Review Generated Messages** → For each profile:
   - Shows profile name and message count
   - Displays all 5 messages as radio button options
   - User selects ONE message per profile
   - First message is selected by default

4. **Use Selected Messages** → Finalizes selection for campaign

## Message Display Format

### For Each Profile:
```
┌─────────────────────────────────────────┐
│ Vipin Kothiyal          5 messages generated │
├─────────────────────────────────────────┤
│ ○ Message 1: "Hi Vipin, I admire..."   │
│ ● Message 2: "Hi Vipin, your post..."  │ ← Selected
│ ○ Message 3: "Hi Vipin, I noticed..."  │
│ ○ Message 4: "Hi again Vipin..."       │
│ ○ Message 5: "Hey Vipin, circling..."  │
├─────────────────────────────────────────┤
│ Profile: linkedin.com/in/vipin-kothiyal │
│ Generated: 2:30:45 PM                   │
└─────────────────────────────────────────┘
```

## Features

### ✅ **Message Parsing**
- Automatically extracts `message1`, `message2`, etc. from API response
- Handles both `messages` object and root-level message properties
- Fallback to full JSON if no messages found

### ✅ **Individual Selection**
- Radio buttons for each message (only one per profile)
- First message selected by default
- Visual feedback for selected messages

### ✅ **Error Handling**
- Shows error badge for failed API calls
- Displays error message clearly
- Continues with successful profiles

### ✅ **Final Output**
- Stores selected message text
- Keeps reference to full API response
- Tracks which message index was selected

## Data Structure

### Final Selected Messages:
```javascript
AppState.selectedMessages = [
  {
    profile: { name: "Vipin Kothiyal", url: "..." },
    selectedMessage: "Hi Vipin, your post about seeking...",
    selectedMessageIndex: 1, // 0-based index
    fullApiResponse: { messages: {...}, profileName: "..." }
  }
]
```

## Benefits

1. **User Control** → Choose best message from 5 options
2. **Clear Display** → Easy to read and compare messages
3. **Flexible Parsing** → Works with your API response format
4. **Error Resilient** → Handles failed API calls gracefully
5. **Complete Data** → Preserves full API response for campaign use

Perfect for selecting the most appropriate message for each LinkedIn connection! 🚀
