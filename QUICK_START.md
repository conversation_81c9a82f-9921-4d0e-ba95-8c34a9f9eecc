# Quick Start

## Setup
1. Start your API (port 7007)
2. Start proxy: `cd server && npm install && npm start`
3. Load extension in Chrome

## New Workflow
1. **Step 3**: Create campaign → Collect profiles → Click "NEXT"
2. **Step 4**: Select profiles → Click "Generate Messages" → API called for selected profiles
3. **Review**: See API responses → Select messages → Click "Use Selected Messages"
4. **Finish**: Click "CREATE CAMPAIGN"

**NEW**: Manual profile selection and message generation workflow! 🚀
