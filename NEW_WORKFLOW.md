# New LinkedIn Extension Workflow

## Overview
The extension now follows a **manual selection and generation** approach instead of automatic API calling.

## Step-by-Step Workflow

### Step 3: Profile Collection
1. **Create Campaign** → Enter campaign name
2. **Collect Profiles** → Use any collection method:
   - LinkedIn Search Results
   - CSV Upload
   - Network Search
   - Manual Profile URLs
3. **Click "NEXT"** → Proceed to Step 4

### Step 4: Profile Selection & Message Generation
1. **Select Profiles** → Choose which profiles to generate messages for:
   - Individual checkboxes for each profile
   - "Select All" / "Deselect All" buttons
   - Shows selected count

2. **Generate Messages** → Click "🤖 Generate Messages for Selected Profiles":
   - Calls `/api/linkedin/messages` for each selected profile
   - Shows progress and API responses
   - Displays all generated messages

3. **Review & Select Messages** → Choose which messages to use:
   - Each message has a checkbox
   - Shows full API response (JSON format)
   - Profile information displayed

4. **Use Selected Messages** → Click "Use Selected Messages":
   - Stores selected messages for campaign
   - Enables "CREATE CAMPAIGN" button

5. **Create Campaign** → Click "CREATE CAMPAIGN" to finalize

## Key Features

### Profile Selection Interface
- ✅ Checkbox for each collected profile
- ✅ Profile name and LinkedIn URL displayed
- ✅ Select All / Deselect All functionality
- ✅ Real-time selected count

### Message Generation
- ✅ API called only for selected profiles
- ✅ Progress indication during generation
- ✅ Error handling for failed API calls
- ✅ Full API response display

### Message Selection
- ✅ Individual message selection
- ✅ Full JSON response visible
- ✅ Profile context for each message
- ✅ Regeneration option

## API Integration

### Request Flow
```
User selects profiles → Click Generate → 
For each selected profile:
  POST http://localhost:7008/api/linkedin/messages
  Body: {"url": "profile-linkedin-url"}
  → Proxy forwards to https://localhost:7007/api/linkedin/messages
  → Your API processes and returns response
  → Response stored and displayed
```

### Response Handling
- ✅ Success responses stored and displayed
- ✅ Error responses handled gracefully
- ✅ All responses shown in JSON format
- ✅ User can select which messages to use

## Benefits

1. **User Control** → Manual selection of profiles and messages
2. **API Efficiency** → Only calls API for selected profiles
3. **Response Review** → User can see and choose API responses
4. **Error Handling** → Failed API calls don't break the workflow
5. **Flexibility** → User can regenerate or select different messages

## Usage Example

1. Collect 10 profiles from LinkedIn search
2. Go to Step 4 → Select 5 profiles
3. Click "Generate Messages" → API called 5 times
4. Review 5 generated messages
5. Select 3 best messages
6. Click "Use Selected Messages"
7. Create campaign with 3 profiles and their messages

Perfect for testing and refining your API responses! 🚀
